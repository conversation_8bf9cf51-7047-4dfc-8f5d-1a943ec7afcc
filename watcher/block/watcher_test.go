package block

import (
	"context"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/config"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/consts"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/db"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/log"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/watcher/native_price"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/watcher/uniswapv2"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/wrapper"
	"math/big"
	"os"
	"testing"
	"time"
)

func init() {
	log.InitLogger(consts.EnvLocal)
	_ = os.Setenv("ENV", consts.EnvLocal)
	dbConfig := config.DbConfig{
		Host:     "localhost",
		Port:     3306,
		User:     "root",
		Password: "5556",
		Dbname:   "cedefi_evm_indexer",
	}

	rdbConfig := config.RdbConfig{
		Host:               "localhost",
		Port:               6379,
		Password:           "",
		Db:                 0,
		InsecureSkipVerify: false,
	}
	db.InitDb(dbConfig)
	db.InitRdb(rdbConfig)

}

func TestParseByBlock(t *testing.T) {
	cli, err := wrapper.Dial("https://rpc.juchain.org")
	if err != nil {
		t.Fatal(err)
	}

	cfg := config.ChainConfig{
		ChainId:          210000,
		RpcUrl:           "https://rpc.juchain.org",
		MultiCallAddr:    "0x71306Fc2d0260DBe0eA4ff07888FA9e8d7fc22d0",
		WNative:          "0x4d1B49B424afd7075d3c063adDf97D5575E1c7E2",
		WNativeDecimals:  18,
		WNativePair:      "0x4cF7fb4674254d2852FeA7FA79438456A9F271c9",
		UDecimals:        18,
		UniswapV2Enabled: true,
	}

	native_price.InitPriceWatcherGroup([]config.ChainConfig{cfg})
	time.Sleep(time.Second * 3)

	parser := uniswapv2.NewParser(cfg)

	pCtx := parseContext{
		ctx:             context.Background(),
		cli:             cli,
		chainId:         big.NewInt(210000),
		uniswapV2Parser: &parser,
	}

	pCtx.parseByBlock(
		5259223,
		5259223)
	select {}

}
