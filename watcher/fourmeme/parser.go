package fourmeme

import (
	"context"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/forta-network/go-multicall"
	"github.com/shopspring/decimal"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/config"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/consts"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/contracts/erc20"
	fmeme "gitlab.jcwork.net/cedefi/cedefi-evm-indexer/contracts/fourmeme/tokenmanagerv2"
	muticalltype "gitlab.jcwork.net/cedefi/cedefi-evm-indexer/contracts/muticall"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/db"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/db/models"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/log"
	types2 "gitlab.jcwork.net/cedefi/cedefi-evm-indexer/types"
	common2 "gitlab.jcwork.net/cedefi/cedefi-evm-indexer/watcher/common"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/watcher/native_price"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/wrapper"
	"go.uber.org/zap"
	"math/big"
)

type Parser struct {
	rpcUrl          string
	cli             *ethclient.Client
	ws              *ethclient.Client
	chainId         *big.Int
	wNative         string
	wNativeDecimals int
	enabled         bool
}

func NewParser(chain config.ChainConfig) Parser {
	if !chain.FourMemeEnabled {
		return Parser{enabled: false}
	}

	cli, err := ethclient.Dial(chain.RpcUrl)
	if err != nil {
		panic(err)
	}

	chainID, err := cli.ChainID(context.Background())
	if err != nil {
		panic(err)
	}

	if new(big.Int).SetUint64(chain.ChainId).Cmp(chainID) != 0 {
		log.Logger.Error("chainId not match", zap.Uint64("chainId", chain.ChainId), zap.Uint64("rpcChainId", chainID.Uint64()))
		panic("chainId not match")
	}

	var ws *ethclient.Client
	for ws == nil {
		ws, err = ethclient.Dial(chain.WsUrl)
		if err != nil {
			log.Logger.Error("dial ws error", zap.Error(err), zap.String("url", chain.WsUrl))
		}
	}

	return Parser{
		rpcUrl:          chain.RpcUrl,
		cli:             cli,
		ws:              ws,
		chainId:         chainID,
		wNative:         chain.WNative,
		wNativeDecimals: chain.WNativeDecimals,
		enabled:         chain.FourMemeEnabled,
	}
}

func (p Parser) ParseSwap(eventLog wrapper.Log) {
	if !p.enabled {
		return
	}

	tokenManagerV2, _ := fmeme.NewTokenManagerV2(common.Address{}, nil)

	switch eventLog.Topics[0].String() {
	case consts.FourMemeTokenPurchaseTopic:
		purchaseEvent, err := tokenManagerV2.ParseTokenPurchase(eventLog.Log)
		if err != nil {
			log.Logger.Error("parse TokenPurchase event error", zap.Error(err))
			return
		}
		p.parseBuy(purchaseEvent)

	case consts.FourMemeTokenSellTopic:
		saleEvent, err := tokenManagerV2.ParseTokenSale(eventLog.Log)
		if err != nil {
			log.Logger.Error("parse TokenSale event error", zap.Error(err))
			return
		}
		p.parseSell(saleEvent)
	}
}

func (p Parser) parseBuy(purchaseLog *fmeme.TokenManagerV2TokenPurchase) {

	tokenModel, err := p.getAndSaveTokenOnChain(purchaseLog.Token.String())
	if err != nil {
		log.Logger.Error("get token error", zap.Error(err))
		return
	}

	pairAddr := purchaseLog.Token //fourmeme 为全局大池子，为了唯一标识，使用token.address作为 pool address
	_, _ = p.getAndSavePairOnChain(pairAddr.String())

	makerAddr := purchaseLog.Account
	baseAmount := purchaseLog.Amount
	quoteAmount := new(big.Int).Sub(purchaseLog.Cost, purchaseLog.Fee)

	nativePrice := decimal.NewFromFloat(native_price.GetPrice(p.chainId.Uint64()))
	volumeInUsd := decimal.NewFromBigInt(quoteAmount, int32(0-p.wNativeDecimals)).Mul(nativePrice)
	priceInNative := decimal.NewFromBigInt(purchaseLog.Price, int32(0-18))
	priceInUsd := priceInNative.Mul(nativePrice)

	totalSupply := decimal.NewFromBigInt(consts.FourMemeTokenSupply, int32(0-tokenModel.Decimals))
	mcInNative := priceInUsd.Mul(totalSupply)
	log.Logger.Info("mc", zap.String("log", mcInNative.String()))

	activity := models.Activity{
		TokenAddress:     purchaseLog.Token.String(),
		PairAddress:      pairAddr.String(),
		BaseAddress:      purchaseLog.Token.String(),
		BaseDecimals:     consts.FourMemeTokenDecimals,
		QuoteAddress:     p.wNative,
		QuoteDecimals:    p.wNativeDecimals,
		Act:              types2.ActivityType_Buy,
		Maker:            makerAddr.String(),
		BaseAmount:       decimal.NewFromBigInt(baseAmount, 0),
		QuoteAmount:      decimal.NewFromBigInt(quoteAmount, 0),
		PriceInNative:    priceInNative,
		PriceInUsd:       priceInUsd,
		NativePriceInUsd: nativePrice,
		VolumeInUsd:      volumeInUsd,
		Height:           int64(purchaseLog.Raw.BlockNumber),
		Tx:               purchaseLog.Raw.TxHash.String(),
		Idx:              int64(purchaseLog.Raw.Index),
		//Ts:               int64(ts), TODO 需要找到合适的交易时间
		DexName: string(consts.DexNameFourMeme),
		ChainId: p.chainId.Uint64(),
	}

	db.GetSwapStoreChannel(p.chainId.Uint64()) <- &activity //batch insert activity

	if !activity.BaseAmount.IsZero() && !activity.QuoteAmount.IsZero() {
		common2.PublishActivity(activity)
	}

	db.UpdatePoolStatsCache(p.chainId, activity.PairAddress, consts.PoolExpireTime,
		consts.PoolStatMcInNativeRedisKey, mcInNative.String(),
		consts.PoolStatPriceInNativeRedisKey, priceInNative.String(),
		consts.PoolStatQuoteAmountRedisKey, purchaseLog.Funds.String(),
		consts.PoolStatBaseAmountRedisKey, purchaseLog.Offers.String(),
	)
}

func (p Parser) parseSell(saleLog *fmeme.TokenManagerV2TokenSale) {

	tokenModel, err := p.getAndSaveTokenOnChain(saleLog.Token.String())
	if err != nil {
		log.Logger.Error("get token error", zap.Error(err))
		return
	}

	pairAddr := saleLog.Token //fourmeme 为全局大池子，为了唯一标识，使用token.address作为 pool address
	makerAddr := saleLog.Account

	baseAmount := saleLog.Amount
	quoteAmount := new(big.Int).Sub(saleLog.Cost, saleLog.Fee)

	nativePrice := decimal.NewFromFloat(native_price.GetPrice(p.chainId.Uint64()))
	volumeInUsd := decimal.NewFromBigInt(quoteAmount, int32(0-p.wNativeDecimals)).Mul(nativePrice)
	priceInNative := decimal.NewFromBigInt(saleLog.Price, int32(0-18))
	priceInUsd := priceInNative.Mul(nativePrice)

	totalSupply := decimal.NewFromBigInt(consts.FourMemeTokenSupply, int32(0-tokenModel.Decimals))
	mcInNative := priceInUsd.Mul(totalSupply)

	activity := models.Activity{
		TokenAddress:     saleLog.Token.String(),
		PairAddress:      pairAddr.String(),
		BaseAddress:      saleLog.Token.String(),
		BaseDecimals:     consts.FourMemeTokenDecimals,
		QuoteAddress:     p.wNative,
		QuoteDecimals:    p.wNativeDecimals,
		Act:              types2.ActivityType_Sell,
		Maker:            makerAddr.String(),
		BaseAmount:       decimal.NewFromBigInt(baseAmount, 0),
		QuoteAmount:      decimal.NewFromBigInt(quoteAmount, 0),
		PriceInNative:    priceInNative,
		PriceInUsd:       priceInUsd,
		NativePriceInUsd: nativePrice,
		VolumeInUsd:      volumeInUsd,
		Height:           int64(saleLog.Raw.BlockNumber),
		Tx:               saleLog.Raw.TxHash.String(),
		Idx:              int64(saleLog.Raw.Index),
		//Ts:               int64(ts), TODO 需要找到合适的交易时间
		DexName: string(consts.DexNameFourMeme),
		ChainId: p.chainId.Uint64(),
	}
	db.GetSwapStoreChannel(p.chainId.Uint64()) <- &activity //batch insert activity

	if !activity.BaseAmount.IsZero() && !activity.QuoteAmount.IsZero() {
		common2.PublishActivity(activity)
	}

	db.UpdatePoolStatsCache(p.chainId, activity.PairAddress, consts.PoolExpireTime,
		consts.PoolStatMcInNativeRedisKey, mcInNative.String(),
		consts.PoolStatPriceInNativeRedisKey, priceInNative.String(),
		consts.PoolStatQuoteAmountRedisKey, saleLog.Funds.String(),
		consts.PoolStatBaseAmountRedisKey, saleLog.Offers.String(),
	)
}

func (p Parser) getAndSavePairOnChain(pairAddress string) (*models.Pool, error) {
	poolModel, _ := db.GetPoolWithCache(p.chainId, pairAddress)
	if poolModel != nil {
		return poolModel, nil
	}

	poolModel = &models.Pool{
		PairAddress:   pairAddress,
		BaseAddress:   pairAddress, //fourmeme 的 pair 就是 token address
		BaseDecimals:  consts.FourMemeTokenDecimals,
		QuoteAddress:  p.wNative,
		QuoteDecimals: p.wNativeDecimals,
		LpMint:        pairAddress,
		DexName:       string(consts.DexNameFourMeme),
		ChainId:       p.chainId.Uint64(),
	}

	err := db.CreatePoolWithCache(poolModel)
	if err != nil {
		log.Logger.Error("create pool error", zap.Error(err))
	}

	return poolModel, nil
}

func (p Parser) getAndSaveTokenOnChain(tokenAddress string) (*models.Token, error) {
	tokenModel, _ := db.GetTokenWithCache(p.chainId, tokenAddress)
	if tokenModel != nil {
		return tokenModel, nil
	}

	caller, err := multicall.Dial(context.Background(), p.rpcUrl)
	if err != nil {
		log.Logger.Error("dial rpc error", zap.Error(err))
		return nil, err
	}

	tokenContract, err := multicall.NewContract(erc20.Erc20MetaData.ABI, tokenAddress)
	if err != nil {
		log.Logger.Error("new erc20 contract error", zap.Error(err))
		return nil, err
	}

	calls, err := caller.Call(nil,
		tokenContract.NewCall(new(muticalltype.StringResult), "name"),
		tokenContract.NewCall(new(muticalltype.StringResult), "symbol"),
		tokenContract.NewCall(new(muticalltype.Uint8Output), "decimals"),
		tokenContract.NewCall(new(muticalltype.BigIntOutput), "totalSupply"),
	)

	name := calls[0].Outputs.(*muticalltype.StringResult).Value
	symbol := calls[1].Outputs.(*muticalltype.StringResult).Value
	decimals := calls[2].Outputs.(*muticalltype.Uint8Output).Value
	totalSupply := calls[3].Outputs.(*muticalltype.BigIntOutput).Value

	tokenModel = &models.Token{
		Address:     tokenAddress,
		Name:        name,
		Symbol:      symbol,
		Image:       "", // TODO 如何链上查询 token 图片
		Decimals:    int(decimals),
		TotalSupply: decimal.NewFromBigInt(totalSupply, 0),
		ChainId:     p.chainId.Uint64(),
	}

	err = db.CreateTokenWithCache(tokenModel)
	if err != nil {
		log.Logger.Error("create token error", zap.Error(err), zap.String("tokenAddress", tokenAddress), zap.Any("total supply", totalSupply))
	}

	return tokenModel, nil
}
