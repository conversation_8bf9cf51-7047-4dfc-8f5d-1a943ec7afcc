package main

import (
	"context"
	"fmt"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/wrapper"
	"log"
	"math/big"
)

var (
	height = "52180604"
	rpcUrl = "https://api.chainup.net/bsc/mainnet/6e3198189f654b7fa468606e8dc791a4/"
)

func main() {
	// 连接到以太坊客户端
	client, err := wrapper.Dial(rpcUrl)
	if err != nil {
		log.Fatalf("Failed to connect to the Ethereum client: %v", err)
	}

	ctx := context.Background()

	// 获取最新区块号
	latestBlockNumber, err := client.BlockNumber(ctx)
	if err != nil {
		log.Fatalf("Failed to get latest block number: %v", err)
	}

	fmt.Printf("Latest block number: %d\n", latestBlockNumber)
	fmt.Println("Getting the latest 20 blocks...")
	fmt.Println("Block Height\tBlock Time")
	fmt.Println("============\t==========")

	// 获取最近20个区块的信息
	for i := 0; i < 20; i++ {
		blockNumber := latestBlockNumber - uint64(i)

		// 获取区块信息
		block, err := client.BlockByNumber(ctx, big.NewInt(int64(blockNumber)))
		if err != nil {
			log.Printf("Failed to get block %d: %v", blockNumber, err)
			continue
		}

		// 将时间戳转换为可读格式
		// 打印区块高度和时间
		fmt.Printf("%d\t\t%d\n", blockNumber, block.Time())
	}
}
