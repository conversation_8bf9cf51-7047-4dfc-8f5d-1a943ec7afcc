package token

import (
	"context"
	"net/http"
	"strconv"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/common/api"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/config"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/consts"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/db"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/log"
	"gitlab.jcwork.net/cedefi/cedefi-rpc/indexer"
	"go.uber.org/zap"
)

type Service struct {
	indexer.UnimplementedTokenServiceServer
	cli        *http.Client
	blockscout *api.BlockscoutClient
	ethCli     *ethclient.Client
}

func NewService(cfg config.ChainConfig) Service {
	client := api.NewBlockscoutClient("https://explorer.juscan.io/api/", "https://explorer.juscan.io/api/v2")
	eth<PERSON>li, err := ethclient.Dial(cfg.RpcUrl)
	if err != nil {
		panic(err)
	}
	return Service{
		ethCli:     ethCli,
		blockscout: client,
	}
}

func (s Service) GetTop10(ctx context.Context, req *indexer.GetTop10Req) (*indexer.GetTop10Resp, error) {
	resp := &indexer.GetTop10Resp{
		Top10: 0,
	}

	if req.ChainId == consts.ChainIdBSC {
		resp.Top10 = 18.56
		return resp, nil
	}

	holdersSummary, err := s.blockscout.GetTopNHoldersSummary(req.TokenAddress, 10)
	if err != nil {
		return nil, err
	}
	resp.Top10 = holdersSummary.Percentage
	return resp, nil
}

func (s Service) GetHolders(ctx context.Context, req *indexer.GetHoldersReq) (*indexer.GetHoldersResp, error) {
	resp := &indexer.GetHoldersResp{
		Holders: 0,
	}

	if req.ChainId == consts.ChainIdBSC {
		resp.Holders = 208
		return resp, nil
	}

	tokenCounters, err := s.blockscout.GetTokenCounters(req.TokenAddress)
	if err != nil {
		return nil, err
	}
	holders, err := strconv.ParseUint(tokenCounters.Holders, 10, 64)
	if err != nil {
		return nil, err
	}
	resp.Holders = holders
	return resp, nil
}

func (s Service) GetHoldersBatch(ctx context.Context, req *indexer.GetHoldersBatchReq) (*indexer.GetHoldersBatchResp, error) {
	resp := &indexer.GetHoldersBatchResp{
		Holders: []*indexer.HoldersData{},
	}

	if req.ChainId == consts.ChainIdBSC {
		for _, v := range req.TokenAddress {
			item := &indexer.HoldersData{
				TokenAddress: v,
				Count:        208,
			}
			resp.Holders = append(resp.Holders, item)
		}
		return resp, nil
	}

	for _, address := range req.TokenAddress {
		oneHolder, err := s.GetHolders(ctx, &indexer.GetHoldersReq{TokenAddress: address})
		if err != nil {
			return nil, err
		}
		resp.Holders = append(resp.Holders, &indexer.HoldersData{TokenAddress: address, Count: oneHolder.Holders})
	}
	return resp, nil
}

func (s Service) GetLockedStatus(ctx context.Context, req *indexer.GetLockedStatusReq) (*indexer.GetLockedStatusResp, error) {
	lockStatus := float64(0) //万分比,init: 0%;

	if req.ChainId == consts.ChainIdBSC {
		return &indexer.GetLockedStatusResp{
			LockedStatus: 10000,
		}, nil
	}

	pool, err := db.FindPoolByPairAddress(req.PoolAddress)
	if err != nil { //haven't found in db
		log.Logger.Error("FindPoolByPairAddress", zap.String("PoolAddress", req.PoolAddress), zap.Error(err))
		return nil, err
	}

	tokenInfo, err := s.blockscout.GetTokenInfo(pool.LpMint)
	if err != nil {
		return nil, err
	}
	if tokenInfo.TotalSupply == "0" {
		lockStatus = 0
	} else {
		lockStatus = 10000
	}

	return &indexer.GetLockedStatusResp{
		LockedStatus: lockStatus,
	}, nil
}

func (s Service) GetCreateTime(ctx context.Context, req *indexer.GetCreateTimeReq) (*indexer.GetCreateTimeResp, error) {

	if req.ChainId == consts.ChainIdBSC {
		return &indexer.GetCreateTimeResp{}, nil
	}

	addressInfo, err := s.blockscout.GetAddressInfo(req.TokenAddress)
	if err != nil {
		return nil, err
	}

	txReceipt, err := s.ethCli.TransactionReceipt(ctx, common.HexToHash(addressInfo.CreationTransactionHash))
	if err != nil {
		return nil, err
	}

	var ts int64
	if req.ChainId == consts.ChainIdJuChain {
		ts = 1742914214 + txReceipt.BlockNumber.Int64()
	}

	return &indexer.GetCreateTimeResp{CreateTime: ts}, nil
}
