```shell
abigen --abi=contracts/erc20/erc20.json --pkg=erc20 --type Erc20 --out=contracts/erc20/erc20.go
```

``` shell juswap
abigen --abi=contracts/juswap/router.json --pkg=juswap --type Router --out=contracts/juswap/router.go
abigen --abi=contracts/juswap/factory.json --pkg=juswap --type Factory --out=contracts/juswap/factory.go
abigen --abi=contracts/juswap/v2pair.json --pkg=juswap --type Pair --out=contracts/juswap/v2pair.go
abigen --abi=contracts/juswap/price_oracle.json --pkg=juswap --type PriceOracle --out=contracts/juswap/price_oracle.go
```

``` shell jamm
abigen --abi=contracts/jamm/Router.json --pkg=jamm --type Router --out=contracts/jamm/router.go
abigen --abi=contracts/jamm/Factory.json --pkg=jamm --type Factory --out=contracts/jamm/factory.go
abigen --abi=contracts/jamm/Pair.json --pkg=jamm --type Pair --out=contracts/jamm/pair.go
```

``` shell uniswapv2
abigen --abi=contracts/uniswapv2/router.json --pkg=uniswapv2 --type Router --out=contracts/uniswapv2/router.go
abigen --abi=contracts/uniswapv2/factory.json --pkg=uniswapv2 --type Factory --out=contracts/uniswapv2/factory.go
abigen --abi=contracts/uniswapv2/pair.json --pkg=uniswapv2 --type Pair --out=contracts/uniswapv2/pair.go
```

``` shell uniswapv3
abigen --abi=contracts/uniswapv3/router.json --pkg=uniswapv3 --type Router --out=contracts/uniswapv3/router.go
abigen --abi=contracts/uniswapv3/factory.json --pkg=uniswapv3 --type Factory --out=contracts/uniswapv3/factory.go
abigen --abi=contracts/uniswapv3/pool.json --pkg=uniswapv3 --type Pair --out=contracts/uniswapv3/pool.go
```

``` shell fourmeme
abigen --abi=contracts/fourmeme/TokenManagerV2.json --pkg=tokenmanagerv2 --type TokenManagerV2 --out=contracts/fourmeme/tokenmanagerv2.go
abigen --abi=contracts/fourmeme/TokenManagerHelper3.json --pkg=tokenmanagerv2 --type TokenManagerHelper3 --out=contracts/fourmeme/tokenmanagerhelper3.go
```
