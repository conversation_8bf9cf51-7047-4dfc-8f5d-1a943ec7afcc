[{"inputs": [{"internalType": "address", "name": "_poolDeployer", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint24", "name": "fee", "type": "uint24"}, {"indexed": true, "internalType": "int24", "name": "tickSpacing", "type": "int24"}], "name": "FeeAmountEnabled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint24", "name": "fee", "type": "uint24"}, {"indexed": false, "internalType": "bool", "name": "whitelistRequested", "type": "bool"}, {"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}], "name": "FeeAmountExtraInfoUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "Owner<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token0", "type": "address"}, {"indexed": true, "internalType": "address", "name": "token1", "type": "address"}, {"indexed": true, "internalType": "uint24", "name": "fee", "type": "uint24"}, {"indexed": false, "internalType": "int24", "name": "tickSpacing", "type": "int24"}, {"indexed": false, "internalType": "address", "name": "pool", "type": "address"}], "name": "PoolCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "lmPoolDeployer", "type": "address"}], "name": "SetLmPoolDeployer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "verified", "type": "bool"}], "name": "WhiteListAdded", "type": "event"}, {"inputs": [{"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint128", "name": "amount0Requested", "type": "uint128"}, {"internalType": "uint128", "name": "amount1Requested", "type": "uint128"}], "name": "collectProtocol", "outputs": [{"internalType": "uint128", "name": "amount0", "type": "uint128"}, {"internalType": "uint128", "name": "amount1", "type": "uint128"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "tokenA", "type": "address"}, {"internalType": "address", "name": "tokenB", "type": "address"}, {"internalType": "uint24", "name": "fee", "type": "uint24"}], "name": "createPool", "outputs": [{"internalType": "address", "name": "pool", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint24", "name": "fee", "type": "uint24"}, {"internalType": "int24", "name": "tickSpacing", "type": "int24"}], "name": "enableFeeAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint24", "name": "", "type": "uint24"}], "name": "feeAmountTickSpacing", "outputs": [{"internalType": "int24", "name": "", "type": "int24"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint24", "name": "", "type": "uint24"}], "name": "feeAmountTickSpacingExtraInfo", "outputs": [{"internalType": "bool", "name": "whitelistRequested", "type": "bool"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint24", "name": "", "type": "uint24"}], "name": "getPool", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lmPoolDeployer", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "poolDeployer", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint24", "name": "fee", "type": "uint24"}, {"internalType": "bool", "name": "whitelistRequested", "type": "bool"}, {"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "setFeeAmountExtraInfo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "uint32", "name": "feeProtocol0", "type": "uint32"}, {"internalType": "uint32", "name": "feeProtocol1", "type": "uint32"}], "name": "setFeeProtocol", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "pool", "type": "address"}, {"internalType": "address", "name": "lmPool", "type": "address"}], "name": "setLmPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_lmPoolDeployer", "type": "address"}], "name": "setLmPoolDeployer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "bool", "name": "verified", "type": "bool"}], "name": "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]