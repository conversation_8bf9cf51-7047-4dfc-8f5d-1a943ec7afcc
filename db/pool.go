package db

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"math/big"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/consts"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/db/models"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/log"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

func CreatePool(pool *models.Pool) error {
	return GetDb().
		Clauses(clause.OnConflict{DoNothing: true}).
		Save(pool).Error
}

func CreatePoolWithCache(pool *models.Pool) error {
	key := fmt.Sprintf(consts.PoolRedisKeyTpl, pool.ChainId, pool.PairAddress)
	expireAt := setHCache(key, consts.PoolBaseRedisKey, pool, consts.PoolExpireTime)
	expireZsetKey := fmt.Sprintf(consts.PoolExpireZsetTpl, pool.ChainId)
	_ = rdb.ZAdd(context.Background(), expireZsetKey, redis.Z{Score: float64(expireAt.Unix()), Member: key})
	return CreatePool(pool)
}

func FindPoolsByTokenAddress(tokenAddress string) ([]*models.Pool, error) {
	var pools []*models.Pool
	d := GetDb()
	err := d.Raw("? UNION ?",
		d.Where("base_address = ?", tokenAddress).Model(&models.Pool{}),
		d.Where("quote_address = ?", tokenAddress).Model(&models.Pool{}),
	).Order("id DESC").Find(&pools).Error
	return pools, err
}

func FindPoolByPairAddress(pairAddr string) (*models.Pool, error) {
	var pool models.Pool
	d := GetDb()
	err := d.Where("pair_address = ?", pairAddr).First(&pool).Error
	return &pool, err
}

func GetPoolWithCache(chainId *big.Int, poolAddress string) (*models.Pool, error) {
	pool, err := getPoolHAllCache(chainId.Uint64(), poolAddress)
	if err == nil && pool != nil {
		log.Logger.Info("get pool from cache", zap.String("poolAddress", poolAddress), zap.Uint64("chainId", chainId.Uint64()))
		return pool, nil
	}
	err = GetDb().Where("pair_address = ?", poolAddress).First(&pool).Error
	if err != nil {
		return nil, err
	}

	_ = setPoolHAllCache(chainId.Uint64(), pool)
	return pool, nil
}

func getPoolHAllCache(chainId uint64, poolAddress string) (*models.Pool, error) {
	var pool models.Pool
	key := fmt.Sprintf(consts.PoolRedisKeyTpl, chainId, poolAddress)
	mResult, err := getFromHAllCache(key)
	if err != nil {
		return nil, err
	}

	if len(mResult) == 0 {
		return nil, nil
	}

	if r, ok := mResult[consts.PoolBaseRedisKey]; ok && r != "" {
		_ = json.Unmarshal([]byte(r), &pool)
	}

	if r, ok := mResult[consts.PoolStatBaseAmountRedisKey]; ok && r != "" {
		pool.StatNowBaseLiq, _ = decimal.NewFromString(r)
	}

	if r, ok := mResult[consts.PoolStatQuoteAmountRedisKey]; ok && r != "" {
		pool.StatNowQuoteLiq, _ = decimal.NewFromString(r)
	}

	if r, ok := mResult[consts.PoolStatMcInNativeRedisKey]; ok && r != "" {
		pool.StatMarketCap, _ = decimal.NewFromString(r)
	}

	if r, ok := mResult[consts.PoolStatPriceInNativeRedisKey]; ok && r != "" {
		pool.StatPrice, _ = decimal.NewFromString(r)
	}

	if r, ok := mResult[consts.PoolStatProgressRedisKey]; ok && r != "" {
		progress, _ := strconv.ParseUint(r, 10, 32)
		pool.StatProgress = int32(progress)
	}

	return &pool, nil
}

func setPoolHAllCache(chainId uint64, pool *models.Pool) error {
	key := fmt.Sprintf(consts.PoolRedisKeyTpl, chainId, pool.PairAddress)
	poolBytes, _ := json.Marshal(pool)
	err := rdb.HSet(context.Background(), key,
		consts.PoolBaseRedisKey, poolBytes,
		consts.PoolStatBaseAmountRedisKey, pool.StatNowBaseLiq.String(),
		consts.PoolStatQuoteAmountRedisKey, pool.StatNowQuoteLiq.String(),
		consts.PoolStatPriceInNativeRedisKey, pool.StatPrice.String(),
		consts.PoolStatMcInNativeRedisKey, pool.StatMarketCap.String(),
	).Err()

	if err != nil {
		log.Logger.Error("redis hset failed", zap.Error(err))
		return err
	}

	log.Logger.Info("set pool cache", zap.String("key", key))

	expireAt := time.Now().Add(consts.PoolExpireTime)
	if err = rdb.ExpireAt(context.Background(), key, expireAt).Err(); err != nil {
		log.Logger.Error("redis expire failed", zap.Error(err))
		return err
	}

	expireZsetKey := fmt.Sprintf(consts.PoolExpireZsetTpl, chainId)
	return rdb.ZAdd(context.Background(), expireZsetKey, redis.Z{Score: float64(expireAt.Unix()), Member: key}).Err()
}

func UpdatePoolStatsCache(chainId *big.Int, poolAddress string, expiration time.Duration, values ...interface{}) {
	key := fmt.Sprintf(consts.PoolRedisKeyTpl, chainId.Uint64(), poolAddress)
	if err := rdb.HSet(context.Background(), key, values).Err(); err != nil {
		log.Logger.Error("redis hset failed", zap.Error(err))
	}

	expireAt := time.Now().Add(expiration)
	if err := rdb.ExpireAt(context.Background(), key, expireAt).Err(); err != nil {
		log.Logger.Error("redis expire failed", zap.Error(err))
	}

	expireZsetKey := fmt.Sprintf(consts.PoolExpireZsetTpl, chainId.Uint64())
	_ = rdb.ZAdd(context.Background(), expireZsetKey, redis.Z{Score: float64(expireAt.Unix()), Member: key})
}
